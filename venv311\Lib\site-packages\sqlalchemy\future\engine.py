# future/engine.py
# Copyright (C) 2005-2025 the SQLAlchemy authors and contributors
# <see AUTHORS file>
#
# This module is part of SQLAlchemy and is released under
# the MIT License: https://www.opensource.org/licenses/mit-license.php
"""2.0 API features.

this module is legacy as 2.0 APIs are now standard.

"""

from ..engine import Connection as Connection  # noqa: F401
from ..engine import create_engine as create_engine  # noqa: F401
from ..engine import Engine as Engine  # noqa: F401
